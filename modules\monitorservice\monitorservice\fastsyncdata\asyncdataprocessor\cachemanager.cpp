#include "cachemanager.h"
#include "log.h"
#include <QDateTime>

CacheManager::CacheManager(QObject *parent)
    : QObject(parent)
{
    logInfo("CacheManager created with default max size: 10000");
}

CacheManager::~CacheManager()
{
    size_t totalCleared = clearAllCaches();
    logInfo(QString("CacheManager destroyed, cleared %1 items from %2 sensor caches")
           .arg(totalCleared).arg(m_caches.size()));
}

void CacheManager::setMaxTotalSize(size_t maxSize)
{
    if (maxSize == 0) {
        logWarnning("Attempt to set max total size to 0, using default value 10000");
        maxSize = 10000;
    }
    
    m_maxTotalSize = maxSize;
    logInfo(QString("Max total cache size set to: %1").arg(maxSize));
    
    emit cacheSizeChanged(m_totalSize.load(), m_maxTotalSize);
}

bool CacheManager::addData(const QString& sensorId, const QList<QSharedPointer<DataItem>>& items)
{
    if (sensorId.isEmpty()) {
        logError("Empty sensor ID provided to addData");
        return false;
    }
    
    if (items.isEmpty()) {
        logTrace(QString("Empty data list provided for sensor: %1").arg(sensorId));
        return true; // 空数据不算错误
    }
    
    // 检查是否会超过总缓存限制
    if (isOverLimit(items.size())) {
        logWarnning(QString("Cache limit exceeded for sensor %1, current: %2, adding: %3, max: %4")
                   .arg(sensorId).arg(m_totalSize.load()).arg(items.size()).arg(m_maxTotalSize));
        return false;
    }
    
    try {
        // 获取或创建传感器缓存
        SensorDataCache* cache = getOrCreateSensorCache(sensorId);
        if (!cache) {
            logError(QString("Failed to get or create cache for sensor: %1").arg(sensorId));
            return false;
        }
        
        size_t sizeBefore = cache->size();
        
        // 添加数据到传感器缓存
        cache->addData(items);
        
        size_t sizeAfter = cache->size();
        int actualAdded = static_cast<int>(sizeAfter - sizeBefore);
        
        // 更新总缓存大小
        updateTotalSize(actualAdded);
        
        logTrace(QString("Successfully added %1 items to sensor %2, total cache size: %3")
                .arg(actualAdded).arg(sensorId).arg(m_totalSize.load()));
        
        emit cacheSizeChanged(m_totalSize.load(), m_maxTotalSize);
        return true;
        
    } catch (const std::exception& e) {
        logError(QString("Exception in addData for sensor %1: %2").arg(sensorId).arg(e.what()));
        return false;
    } catch (...) {
        logError(QString("Unknown exception in addData for sensor: %1").arg(sensorId));
        return false;
    }
}

SensorDataCache* CacheManager::getSensorCache(const QString& sensorId)
{
    if (sensorId.isEmpty()) {
        logError("Empty sensor ID provided to getSensorCache");
        return nullptr;
    }

    QReadLocker lock(&m_cachesLock);
    auto it = m_caches.find(sensorId);
    if (it != m_caches.end()) {
        return it->get();
    }

    return nullptr;
}

QStringList CacheManager::getAllSensorIds() const
{
    QReadLocker lock(&m_cachesLock);
    QStringList result;
    result.reserve(m_caches.size());

    for (auto it = m_caches.begin(); it != m_caches.end(); ++it) {
        result.append(it.key());
    }

    return result;
}

bool CacheManager::isOverLimit(size_t additionalSize) const
{
    size_t currentSize = m_totalSize.load();
    return (currentSize + additionalSize) > m_maxTotalSize;
}

QHash<QString, size_t> CacheManager::getCacheStatistics() const
{
    QReadLocker lock(&m_cachesLock);
    QHash<QString, size_t> stats;

    for (auto it = m_caches.begin(); it != m_caches.end(); ++it) {
        stats[it.key()] = it->get()->size();
    }

    return stats;
}

size_t CacheManager::clearSensorCache(const QString& sensorId)
{
    if (sensorId.isEmpty()) {
        logError("Empty sensor ID provided to clearSensorCache");
        return 0;
    }

    QWriteLocker lock(&m_cachesLock);
    auto it = m_caches.find(sensorId);
    if (it != m_caches.end()) {
        size_t clearedCount = it->get()->size();
        m_caches.erase(it);

        // 更新总缓存大小
        updateTotalSize(-static_cast<int>(clearedCount));

        logInfo(QString("Cleared cache for sensor %1, removed %2 items")
               .arg(sensorId).arg(clearedCount));

        emit cacheSizeChanged(m_totalSize.load(), m_maxTotalSize);
        return clearedCount;
    }

    logTrace(QString("No cache found for sensor: %1").arg(sensorId));
    return 0;
}

size_t CacheManager::clearAllCaches()
{
    QWriteLocker lock(&m_cachesLock);
    size_t totalCleared = m_totalSize.load();

    m_caches.clear();
    m_totalSize.store(0);

    logInfo(QString("Cleared all caches, removed %1 items").arg(totalCleared));

    emit cacheSizeChanged(0, m_maxTotalSize);
    return totalCleared;
}

SensorDataCache* CacheManager::getOrCreateSensorCache(const QString& sensorId)
{
    // 首先尝试读锁获取
    {
        QReadLocker readLock(&m_cachesLock);
        auto it = m_caches.find(sensorId);
        if (it != m_caches.end()) {
            return it->get();
        }
    }

    // 需要创建新缓存，使用写锁
    QWriteLocker writeLock(&m_cachesLock);

    // 双重检查，防止在获取写锁期间其他线程已创建
    auto it = m_caches.find(sensorId);
    if (it != m_caches.end()) {
        return it->get();
    }

    try {
        // 创建新的传感器缓存
        std::unique_ptr<SensorDataCache> cache(new SensorDataCache(sensorId));
        SensorDataCache* cachePtr = cache.get();
        m_caches[sensorId] = std::move(cache);

        logInfo(QString("Created new cache for sensor: %1").arg(sensorId));
        return cachePtr;

    } catch (const std::exception& e) {
        logError(QString("Exception creating cache for sensor %1: %2").arg(sensorId).arg(e.what()));
        return nullptr;
    } catch (...) {
        logError(QString("Unknown exception creating cache for sensor: %1").arg(sensorId));
        return nullptr;
    }
}

void CacheManager::updateTotalSize(int delta)
{
    if (delta == 0) {
        return;
    }

    size_t oldSize = m_totalSize.load();
    size_t newSize;

    if (delta > 0) {
        newSize = oldSize + static_cast<size_t>(delta);
    } else {
        size_t absDelta = static_cast<size_t>(-delta);
        newSize = (oldSize >= absDelta) ? (oldSize - absDelta) : 0;
    }

    m_totalSize.store(newSize);

    logTrace(QString("Total cache size updated: %1 -> %2 (delta: %3)")
            .arg(oldSize).arg(newSize).arg(delta));
}

void CacheManager::recalculateTotalSize()
{
    QReadLocker lock(&m_cachesLock);
    size_t calculatedSize = 0;

    for (auto it = m_caches.begin(); it != m_caches.end(); ++it) {
        calculatedSize += it->get()->size();
    }

    size_t oldSize = m_totalSize.load();
    m_totalSize.store(calculatedSize);

    if (oldSize != calculatedSize) {
        logWarnning(QString("Total cache size corrected: %1 -> %2").arg(oldSize).arg(calculatedSize));
        emit cacheSizeChanged(calculatedSize, m_maxTotalSize);
    }
}
